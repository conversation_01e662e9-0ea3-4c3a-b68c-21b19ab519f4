import random
import pandas as pd
from io import StringIO
from typing import Dict, List, Tuple
from dataclasses import dataclass
from datetime import datetime
import os

# 设备维检修关键逻辑表格数据
data = """
操作类型,排查指标/排查动作,指标状况/排查结果,排查推论,故障类型
现场查看,分离器入口压力开关动作是否发生,是/否,若是，说明分离器入口压力开关动作触发了ESD导致安全阀关断；若不是，安全阀关断不是由分离器入口压力开关动作导致的。,自控
现场查看,查看分离器入口压力是否稳定未超限,入口压力值为X，阈值为XX。是/否超限,若超限，表明可能是分离器产生的压力开关触发的安全阀关断；若没有超限，则安全阀关断不是由分离器入口压力开关动作导致的。,自控
现场查看,查看井场井口压力趋势是否有异常波动点,井下安全阀压力值为X，阈值为XX，是/否超限。地面安全阀压力值为X，阈值为XX，是/否超限,若井下/地面安全阀超限，表明可能是由于井下/地面压力超限触发的安全阀关断；若没有超限，则安全阀关断不是由井场压力超限导致的。,自控
现场查看,查看井场出口温度趋势是否有异常波动点,井场出口温度为X，阈值为95℃。是/否 超限,若超限，说明安全阀关断是由于井场温度过高导致的；若没有超限，则说明不是由于井场温度导致的安全阀关闭。,自控
现场查看,查看液压控制柜内ESD按钮是否被按下,是/否,若是，则说明是人为操作液压控制柜触发的ESD关断命令导致的安全阀关断；若不是，则安全阀关断不是由于人为操作液压控制柜导致的。,自控
现场查看,查看现场RTU机柜是否有人为命令情况,是/否,若是，则说明是人为操作RTU机柜导致的安全阀关断；若不是，则安全阀关断不是由于人为在RTU机柜操作导致的。,自控
现场查看,查看站控室远程ESD关阀命令,是/否,若是，则说明是人为在站控室远程操纵了ESD关阀命令导致的安全阀关断；若不是，则安全阀关断不是由于站控室里人为操作导致的。,自控
现场查看,查看井下安全阀（SCSSV）井口针阀及接头是否完好无外漏,是/否,若是，则安全阀关断不是由于井下安全阀（SCSSV）井口针阀及接口导致的；若不是，说明是井下安全阀（SCSSV）井口针阀及接口损坏导致的安全阀关断,井控
现场查看,查看地面安全阀（SSV）井口针阀及接头是否完好无外漏,是/否,若是，则安全阀关断不是由于地面安全阀（SSV）井口针阀及接口导致的；若不是，说明是地面安全阀（SSV）井口针阀及接口损坏导致的安全阀关断,井控
现场查看,查看高低压导阀及接头是否完好无外漏,是/否,若是，则安全阀关断不是由于高低压导阀及接头损坏导致的；若不是，说明是高低压导阀及接头损坏导致的安全阀关断。,井控
现场查看,查看液控柜内部液压管线与接头是否完好无外漏,是/否,若是，则安全阀关断不是由于液控柜内部液压管线与接头损坏导致的；若不是，说明是液控柜内部液压管线与接头损坏导致的安全阀关断。,井控
现场查看,查看易熔塞压力开关接头是否完好无外漏且压力表读数是否低于50psi,是/否完好无外漏。压力阙值为50psi是/否低于阙值,若不是完好无外漏或低于阙值则说明是易融塞损坏导致的安全阀关断；若是完好无外漏且高于阙值则说明安全阀关断不是由于易融塞导致的,井控
现场查看,查看井口液压管线及接头是否完好无渗漏,是/否,若是，则说明安全阀关断不是由于井口液压管线及接头导致的；若不是，说明是井口液压管线及接头损坏导致的安全阀关断。,井控
现场查看,手动起压打开地面安全阀后查看液控柜各项压力指标是否稳定不掉压,是/否稳定不掉压。各个指标设定的压力范围是否越限,若是则说明安全阀报警不是由于液控柜内组件故障引起的；若不是如果存在某项指标大小超过或者低于正常范围则说明可能是由于液控柜内某组件故障引起的,井控
现场查看,检查控制柜UPS模块的供电电压是否稳定查看继电器接线头是否松动,电压值为()正常供电范围为()是/否超限。是/否松动,若供电电压稳定继电器接线头无松动说明液控柜电源正常无故障；否则可能是UPS模块给继电器供电电压不稳或继电器接线头松动导致了故障导致继电器断电致使电磁阀回油触发安全阀关闭,电气
现场查看,核查现场低压配电柜液压柜接线是否松动是否出现虚接现象,是/否,若是，则安全阀关断可能是由于低压配电柜液压柜接线松动出现虚接的情况导致的；若不是，说明安全阀关断与低压配电柜液压柜的接线无关。,电气
现场查看,核查现场电压是否正常，是否存在缺相和欠过电压情况,是/否,若是，则安全阀关断可能是由于现场电压缺相或欠过电压导致的；若不是，则说明安全阀关断与现场电压缺项或欠过电压无关。,电气
电话询问,询问变电所故障所在的电线线路历史上是否存在电压波动，如果发生如跳闸之类的电压波动事件,是/否,若是，则说明安全阀关断可能是由于所在线路的电压波动导致的；否不是，则说明安全阀关断与现场电压波动无关。,电气
现场查看,检查是否存在位于多个通信链路相互之间存在隔离的线阀组上的多个井场的安全阀同时关断,是/否,若是，则可以排除是由于某一处硬件故障而导致四口井场的安全阀同时关断的可能，故障大概率出自电气方面。若不是，则需要进一步从其他方面来判断故障来源。,电气
"""

@dataclass
class FaultScenario:
    """故障场景数据类"""
    fault_type: str
    description: str
    key_indicators: List[str]
    probability: float = 1.0

@dataclass
class InspectionStep:
    """检查步骤数据类"""
    operation_type: str
    action: str
    result_template: str
    reasoning_template: str
    fault_type: str

# 将数据转换为DataFrame
df = pd.read_csv(StringIO(data))

# 故障类型列表
FAULT_TYPES = ["自控", "井控", "电气"]

# 参数范围配置：正常范围、异常范围、单位（仅保留检测所需参数）
PARAMETER_RANGES = {

    # 压力相关参数（需要根据实际技术规范修正）
    "pressure": {"normal": (-1, 45), "abnormal": (50, 75), "unit": "MPa"}, # 分离器入口压力

    "downhole_pressure": {"normal": (5000, 18000), "abnormal": (20001, 25000), "unit": "kPa"}, # 井下安全阀压力（不超过20000kPa）
    "surface_pressure": {"normal": (2000, 5000), "abnormal": (5501, 7000), "unit": "kPa"}, # 地面安全阀压力（不超过5500kPa）
    "hydraulic_pressure": {"normal": (55, 80), "abnormal": (30, 49), "unit": "psi"}, # 易熔塞压力（大于50psi正常）

    "temperature": {"normal": (25, 85), "abnormal": (90, 115), "unit": "℃"}, # 井场出口温度

    # 电气相关参数
    "voltage": {"normal": (220, 240), "abnormal": (200, 215), "unit": "V"} # UPS供电电压
}

class FaultDiagnosisGenerator:
    """故障诊断COT生成器"""

    def __init__(self, data_df: pd.DataFrame):
        """初始化生成器

        Args:
            data_df: 包含故障诊断逻辑的DataFrame
        """
        self.df = data_df
        self.inspection_steps = self._parse_inspection_steps()
        self.fault_scenarios = self._define_fault_scenarios()

    def _parse_inspection_steps(self) -> Dict[str, List[InspectionStep]]:
        """解析检查步骤数据"""
        steps_by_type = {fault_type: [] for fault_type in FAULT_TYPES}

        for _, row in self.df.iterrows():
            step = InspectionStep(
                operation_type=row['操作类型'],
                action=row['排查指标/排查动作'],
                result_template=row['指标状况/排查结果'],
                reasoning_template=row['排查推论'],
                fault_type=row['故障类型']
            )
            steps_by_type[row['故障类型']].append(step)

        return steps_by_type

    def _define_fault_scenarios(self) -> Dict[str, List[FaultScenario]]:
        """定义各类故障场景"""
        scenarios = {
            "自控": [
                FaultScenario("自控", "分离器入口压力超限触发ESD", ["分离器入口压力", "压力开关"], 0.2),
                FaultScenario("自控", "井场出口温度过高导致安全阀关断", ["井场出口温度", "温度传感器"], 0.2),
                FaultScenario("自控", "井下安全阀压力超限触发关断", ["井下安全阀", "压力超限"], 0.2),
                FaultScenario("自控", "地面安全阀压力超限触发关断", ["地面安全阀", "压力超限"], 0.2),
                FaultScenario("自控", "人为操作RTU机柜误触发ESD", ["RTU机柜", "人为命令"], 0.1),
                FaultScenario("自控", "液压控制柜ESD按钮被误按", ["ESD按钮", "液压控制柜"], 0.05),
                FaultScenario("自控", "站控室远程ESD关阀命令", ["远程命令", "站控室"], 0.05)
            ],
            "井控": [
                FaultScenario("井控", "井下安全阀井口针阀外漏", ["井下安全阀", "针阀", "外漏"], 0.25),
                FaultScenario("井控", "液控柜液压管线破裂", ["液控柜", "液压管线", "破裂"], 0.2),
                FaultScenario("井控", "易熔塞压力低于阈值", ["易熔塞", "压力开关"], 0.2),
                FaultScenario("井控", "地面安全阀针阀接头损坏", ["地面安全阀", "针阀接头"], 0.15),
                FaultScenario("井控", "高低压导阀接头外漏", ["高低压导阀", "接头外漏"], 0.1),
                FaultScenario("井控", "井口液压管线渗漏", ["井口液压管线", "渗漏"], 0.1)
            ],
            "电气": [
                FaultScenario("电气", "UPS供电电压不稳导致继电器断电", ["UPS模块", "供电电压", "继电器"], 0.3),
                FaultScenario("电气", "低压配电柜接线虚接", ["配电柜", "接线虚接"], 0.25),
                FaultScenario("电气", "现场电压缺相或欠过电压", ["现场电压", "缺相", "欠过电压"], 0.2),
                FaultScenario("电气", "变电所线路电压波动", ["变电所", "电压波动"], 0.15),
                FaultScenario("电气", "多井场同时关断的电气故障", ["多井场", "同时关断"], 0.1)
            ]
        }
        return scenarios

    def _generate_realistic_value(self, param_type: str, is_abnormal: bool = False) -> Tuple[float, str]:
        """生成真实的参数值

        Args:
            param_type: 参数类型，支持以下类型：
                      pressure: 压力（分离器入口压力，MPa）
                      downhole_pressure: 井下安全阀压力（kPa，不超过20000）
                      surface_pressure: 地面安全阀压力（kPa，不超过5500）
                      temperature: 温度（井场出口温度，℃）
                      voltage: 电压（UPS供电电压，V）
                      hydraulic_pressure: 液压压力（易熔塞压力，psi，>50正常）
            is_abnormal: 是否生成异常值

        Returns:
            (数值, 单位)
        """
        if param_type not in PARAMETER_RANGES:
            return 0.0, ""

        config = PARAMETER_RANGES[param_type]
        range_key = "abnormal" if is_abnormal else "normal"
        min_val, max_val = config[range_key]
        value = round(random.uniform(min_val, max_val), 1)

        return value, config["unit"]

    def _is_actual_fault_found(self, fault_reasoning: str, fault_step: InspectionStep) -> bool:
        """检查推理步骤是否真的发现了故障

        Args:
            fault_reasoning: 推理步骤的文本
            fault_step: 故障步骤对象

        Returns:
            是否真的发现了故障
        """
        # 检查易熔塞相关故障
        if "易熔塞" in fault_step.action:
            # 如果推理分析说"不是由于易融塞导致的"，则没有发现故障
            if "不是由于易融塞导致的" in fault_reasoning:
                return False
            # 如果推理分析说"是易融塞损坏导致的"，则发现了故障
            if "是易融塞损坏导致的" in fault_reasoning:
                return True
            # 其他情况根据压力值判断
            return "低于阈值" in fault_reasoning

        # 检查其他类型的故障指示词
        fault_indicators = [
            "超限", "异常", "松动", "虚接", "外漏", "渗漏",
            "损坏", "故障", "波动", "被按下", "人为命令"
        ]

        return any(indicator in fault_reasoning for indicator in fault_indicators)

    def _has_abnormal_result(self, result: str) -> bool:
        """检查检查结果是否显示异常情况

        Args:
            result: 检查结果文本

        Returns:
            是否显示异常情况
        """
        # 明确的异常指示词
        abnormal_indicators = [
            "是，超限", "是超限", "是松动", "松动", "外漏", "渗漏",
            "损坏", "波动", "被按下", "缺相", "欠过电压", "不稳定"
        ]

        # 明确的正常指示词
        normal_indicators = [
            "否，未超限", "未超限", "否超限", "否松动", "否，未松动",
            "无外漏", "无渗漏"
        ]

        # 首先检查是否有明确的正常指示
        if any(indicator in result for indicator in normal_indicators):
            return False

        # 然后检查是否有异常指示
        if any(indicator in result for indicator in abnormal_indicators):
            return True

        # 特殊处理：单独的"否"在某些上下文中表示异常
        if result.strip() == "否":
            return True

        return False

    def _extract_specific_fault_cause(self, reasoning_steps: List[str]) -> str:
        """从推理步骤中提取具体的故障原因

        Args:
            reasoning_steps: 推理步骤列表

        Returns:
            具体的故障原因描述，如果没有找到则返回None
        """
        for step in reasoning_steps:
            # 检查是否包含明确的故障指示
            if "说明是" in step and "导致的安全阀关断" in step:
                # 提取具体故障原因
                if "人为在站控室远程操纵了ESD关阀命令" in step:
                    return "人为在站控室远程操纵ESD关阀命令"
                elif "人为操作RTU机柜" in step:
                    return "人为操作RTU机柜误触发ESD"
                elif "分离器入口压力开关动作触发了ESD" in step:
                    return "分离器入口压力开关动作触发ESD"
                elif "分离器产生的压力开关触发的安全阀关断" in step:
                    return "分离器入口压力超限触发ESD"
                elif "现场电压缺相或欠过电压" in step:
                    return "现场电压缺相或欠过电压"
                elif "所在线路的电压波动" in step:
                    return "电力线路电压波动导致故障"
                elif "UPS模块给继电器供电电压不稳" in step:
                    return "UPS供电电压不稳导致继电器断电"
                elif "地面安全阀" in step and "损坏" in step:
                    return "地面安全阀针阀接头损坏"
                elif "井下安全阀" in step and "损坏" in step:
                    return "井下安全阀针阀接头损坏"
                elif "高低压导阀" in step and "损坏" in step:
                    return "高低压导阀接头外漏"
                elif "液控柜内部液压管线" in step and "损坏" in step:
                    return "液控柜液压管线破裂"
                elif "井口液压管线" in step and "损坏" in step:
                    return "井口液压管线及接头损坏"
                elif "易融塞损坏" in step:
                    return "易熔塞压力低于阈值导致安全阀关断"
                elif "低压配电柜液压柜接线松动出现虚接" in step:
                    return "低压配电柜接线虚接导致供电异常"

        return None

    def _select_inspection_steps(self, fault_type: str, scenario: FaultScenario, num_steps: int = None) -> List[InspectionStep]:
        """选择相关的检查步骤

        Args:
            fault_type: 故障类型
            scenario: 故障场景
            num_steps: 检查步骤数量，默认为3-5步

        Returns:
            选中的检查步骤列表
        """
        if num_steps is None:
            num_steps = random.randint(3, 5)

        available_steps = self.inspection_steps[fault_type]

        # 优先选择与场景关键指标相关的步骤
        relevant_steps = []
        other_steps = []

        for step in available_steps:
            is_relevant = any(indicator in step.action for indicator in scenario.key_indicators)
            if is_relevant:
                relevant_steps.append(step)
            else:
                other_steps.append(step)

        # 确保至少包含一些相关步骤
        selected_steps = relevant_steps[:min(len(relevant_steps), max(1, num_steps // 2))]

        # 补充其他步骤
        remaining_count = num_steps - len(selected_steps)
        if remaining_count > 0:
            selected_steps.extend(random.sample(other_steps, min(remaining_count, len(other_steps))))

        return selected_steps[:num_steps]

    def _generate_inspection_result(self, step: InspectionStep, scenario: FaultScenario, is_fault_step: bool) -> str:
        """生成检查结果

        Args:
            step: 检查步骤
            scenario: 故障场景
            is_fault_step: 是否为导致故障的步骤

        Returns:
            格式化的检查结果
        """
        # 解析结果模板中的参数
        result_template = step.result_template

        # 处理检测所需的参数类型
        # 1. 压力相关参数（分离器入口压力、井下安全阀压力、地面安全阀压力）
        if "压力值为X" in result_template:
            # 处理井下安全阀压力值
            if "井下安全阀压力值为X" in result_template:
                downhole_pressure_val, pressure_unit = self._generate_realistic_value("downhole_pressure", is_fault_step)
                downhole_threshold_val, _ = self._generate_realistic_value("downhole_pressure", False)
                result_template = result_template.replace("井下安全阀压力值为X", f"井下安全阀压力值为{downhole_pressure_val}{pressure_unit}")

                # 处理井下安全阀的阈值
                if "阈值为XX" in result_template:
                    # 找到第一个阈值并替换
                    result_template = result_template.replace("阈值为XX", f"阈值为{downhole_threshold_val}{pressure_unit}", 1)

                # 根据实际数值判断井下安全阀是否超限
                if "井下安全阀压力值为" in result_template and "是/否超限" in result_template:
                    if downhole_pressure_val > downhole_threshold_val:
                        # 找到井下安全阀相关的超限判断并替换
                        result_template = result_template.replace("是/否超限", "是，超限", 1)
                    else:
                        result_template = result_template.replace("是/否超限", "否，未超限", 1)

            # 处理地面安全阀压力值
            if "地面安全阀压力值为X" in result_template:
                surface_pressure_val, pressure_unit = self._generate_realistic_value("surface_pressure", is_fault_step)
                surface_threshold_val, _ = self._generate_realistic_value("surface_pressure", False)
                result_template = result_template.replace("地面安全阀压力值为X", f"地面安全阀压力值为{surface_pressure_val}{pressure_unit}")

                # 处理地面安全阀的阈值（第二个阈值）
                if "阈值为XX" in result_template:
                    result_template = result_template.replace("阈值为XX", f"阈值为{surface_threshold_val}{pressure_unit}")

                # 根据实际数值判断地面安全阀是否超限
                if "地面安全阀压力值为" in result_template and "是/否超限" in result_template:
                    if surface_pressure_val > surface_threshold_val:
                        # 找到地面安全阀相关的超限判断并替换
                        result_template = result_template.replace("是/否超限", "是，超限")
                    else:
                        result_template = result_template.replace("是/否超限", "否，未超限")

            # 处理其他压力值（分离器入口压力等）
            if "入口压力值为X" in result_template:
                pressure_val, pressure_unit = self._generate_realistic_value("pressure", is_fault_step)
                threshold_val, _ = self._generate_realistic_value("pressure", False)
                result_template = result_template.replace("入口压力值为X", f"入口压力值为{pressure_val}{pressure_unit}")
                if "阈值为XX" in result_template:
                    result_template = result_template.replace("阈值为XX", f"阈值为{threshold_val}{pressure_unit}")

                # 根据实际数值判断是否超限
                if "是/否超限" in result_template:
                    if pressure_val > threshold_val:
                        result_template = result_template.replace("是/否超限", "是，超限")
                    else:
                        result_template = result_template.replace("是/否超限", "否，未超限")

            # 处理一般的压力值为X（兜底处理）
            if "压力值为X" in result_template and "安全阀压力值为X" not in result_template:
                pressure_val, pressure_unit = self._generate_realistic_value("pressure", is_fault_step)
                threshold_val, _ = self._generate_realistic_value("pressure", False)
                result_template = result_template.replace("压力值为X", f"压力值为{pressure_val}{pressure_unit}")
                if "阈值为XX" in result_template:
                    result_template = result_template.replace("阈值为XX", f"阈值为{threshold_val}{pressure_unit}")

        # 2. 温度相关参数（井场出口温度）
        if "温度为X" in result_template:
            temp_val, temp_unit = self._generate_realistic_value("temperature", is_fault_step)
            result_template = result_template.replace("温度为X", f"温度为{temp_val}{temp_unit}")

            # 处理温度超限判断 - 根据实际数值比较
            if "阈值为95℃" in result_template and "是/否 超限" in result_template:
                if temp_val > 95:
                    # 实际温度超过95℃
                    result_template = result_template.replace("是/否 超限", "是，超限")
                else:
                    # 实际温度未超过95℃
                    result_template = result_template.replace("是/否 超限", "否，未超限")

        # 3. 电压相关参数（UPS供电电压）
        if "电压值为()" in result_template:
            voltage_val, voltage_unit = self._generate_realistic_value("voltage", is_fault_step)
            normal_range = PARAMETER_RANGES["voltage"]["normal"]
            result_template = result_template.replace("电压值为()", f"电压值为{voltage_val}{voltage_unit}")
            result_template = result_template.replace("正常供电范围为()", f"正常供电范围为{normal_range[0]}-{normal_range[1]}{voltage_unit}")

            # 根据实际电压值判断是否超限
            if "是/否超限" in result_template:
                if voltage_val < normal_range[0] or voltage_val > normal_range[1]:
                    result_template = result_template.replace("是/否超限", "是超限")
                else:
                    result_template = result_template.replace("是/否超限", "否超限")

            # 根据是否为故障步骤判断接线头是否松动
            if "是/否松动" in result_template:
                if is_fault_step:
                    result_template = result_template.replace("是/否松动", "是松动")
                else:
                    result_template = result_template.replace("是/否松动", "否松动")

        # 4. 液压压力（易熔塞压力）- 大于50psi正常，小于50psi异常
        if "50psi" in result_template:
            hydraulic_val, hydraulic_unit = self._generate_realistic_value("hydraulic_pressure", is_fault_step)
            result_template = result_template.replace("压力阙值为50psi", f"压力阈值为50{hydraulic_unit}")
            # 根据是否为故障步骤生成相应结果
            if is_fault_step:
                # 故障情况：压力低于50psi
                result_template = result_template.replace("是/否低于阙值", f"是，实测值为{hydraulic_val}{hydraulic_unit}，低于阈值")
            else:
                # 正常情况：压力高于50psi
                result_template = result_template.replace("是/否低于阙值", f"否，实测值为{hydraulic_val}{hydraulic_unit}，高于阈值")

        # 生成是/否结果（仅处理未被上面具体逻辑处理的情况）
        if is_fault_step:
            # 根据故障场景生成相应的异常结果
            if "是/否" in result_template:
                if any(keyword in step.action for keyword in ["外漏", "松动", "虚接", "波动"]):
                    result_template = result_template.replace("是/否", "是")
                # 注意：超限判断已经在上面的具体逻辑中处理，这里不再处理
                elif "是/否" in result_template and "超限" not in result_template:
                    result_template = result_template.replace("是/否", "是")
        else:
            # 正常情况（仅处理未被上面具体逻辑处理的情况）
            if "是/否" in result_template and "超限" not in result_template:
                result_template = result_template.replace("是/否", "否")

        return result_template

    def generate_fault_scenario(self) -> str:
        """生成完整的故障场景和COT推理过程"""
        try:
            # 随机选择故障类型和场景
            fault_type = random.choice(FAULT_TYPES)
            scenario = random.choices(
                self.fault_scenarios[fault_type],
                weights=[s.probability for s in self.fault_scenarios[fault_type]]
            )[0]

            # 选择检查步骤
            inspection_steps = self._select_inspection_steps(fault_type, scenario)

            # 生成推理过程
            reasoning_steps = []
            fault_step_index = random.randint(0, len(inspection_steps) - 1)  # 随机确定哪一步发现故障

            for i, step in enumerate(inspection_steps):
                is_fault_step = (i == fault_step_index)

                # 生成检查结果
                result = self._generate_inspection_result(step, scenario, is_fault_step)

                # 生成推理逻辑
                reasoning = step.reasoning_template
                if is_fault_step:
                    # 确保推理指向故障原因
                    if "若是" in reasoning and "若不是" in reasoning:
                        reasoning_part = reasoning.split("若不是")[0].replace("若是，", "").replace("若是", "").strip()
                        if not reasoning_part.endswith("。"):
                            reasoning_part += "。"
                    else:
                        reasoning_part = reasoning.replace("若是，", "").replace("若是", "").strip()
                else:
                    # 非故障步骤：根据实际检查结果判断
                    if self._has_abnormal_result(result):
                        # 如果检查结果显示异常，使用故障推理
                        if "若不是" in reasoning:
                            reasoning_part = reasoning.split("若不是")[1].replace("，", "").strip()
                            if reasoning_part.startswith("则"):
                                reasoning_part = reasoning_part[1:]
                            if not reasoning_part.endswith("。"):
                                reasoning_part += "。"
                        else:
                            reasoning_part = "检查发现异常情况，可能是故障原因。"
                    else:
                        # 检查结果正常，排除该原因
                        if "若不是" in reasoning:
                            reasoning_part = reasoning.split("若不是")[1].replace("，", "").strip()
                            if reasoning_part.startswith("则"):
                                reasoning_part = reasoning_part[1:]
                            if not reasoning_part.endswith("。"):
                                reasoning_part += "。"
                        else:
                            reasoning_part = "检查结果正常，排除此项故障原因。"

                step_description = f"{i+1}. 【{step.operation_type}】{step.action}\n   检查结果：{result}\n   推理分析：{reasoning_part.strip()}"
                reasoning_steps.append(step_description)

            # 生成结论 - 基于实际发现故障的步骤
            fault_step = inspection_steps[fault_step_index]

            # 检查是否真的发现了故障（通过分析推理步骤）
            fault_reasoning = reasoning_steps[fault_step_index]
            is_actual_fault = self._is_actual_fault_found(fault_reasoning, fault_step)

            # 根据故障步骤生成具体的故障原因描述
            # 注意：需要确保故障原因与实际检查结果一致

            # 首先尝试从推理步骤中提取具体故障原因
            specific_fault_cause = self._extract_specific_fault_cause(reasoning_steps)

            if specific_fault_cause:
                actual_fault_cause = specific_fault_cause
            elif not is_actual_fault:
                # 如果预选的故障步骤实际上没有发现故障，使用通用描述
                actual_fault_cause = f"{fault_type}系统相关故障"
            elif "温度" in fault_step.action and "超限" in fault_step.action:
                actual_fault_cause = "井场出口温度过高导致安全阀关断"
            elif "井下安全阀压力" in fault_step.action:
                actual_fault_cause = "井下安全阀压力超限触发关断"
            elif "地面安全阀压力" in fault_step.action:
                actual_fault_cause = "地面安全阀压力超限触发关断"
            elif "分离器入口压力" in fault_step.action:
                actual_fault_cause = "分离器入口压力超限触发ESD"
            elif "低压配电柜" in fault_step.action or "虚接" in fault_step.action or "接线松动" in fault_step.action:
                actual_fault_cause = "低压配电柜接线虚接导致供电异常"
            elif "UPS" in fault_step.action or ("电压" in fault_step.action and "低压配电柜" not in fault_step.action):
                actual_fault_cause = "UPS供电电压不稳导致继电器断电"
            elif "易熔塞" in fault_step.action:
                # 根据实际检查结果判断故障原因
                # 检查推理步骤中的实际结果
                fault_reasoning = reasoning_steps[fault_step_index]
                if "不是由于易融塞导致的" in fault_reasoning:
                    # 如果推理明确说不是易熔塞导致的，使用通用故障原因
                    actual_fault_cause = "井控系统相关故障"
                elif "低于阈值" in fault_reasoning:
                    actual_fault_cause = "易熔塞压力低于阈值导致安全阀关断"
                else:
                    actual_fault_cause = "易熔塞相关故障"
            elif "ESD按钮" in fault_step.action:
                actual_fault_cause = "液压控制柜ESD按钮被误按"
            elif "RTU" in fault_step.action:
                actual_fault_cause = "人为操作RTU机柜误触发ESD"
            elif "站控室" in fault_step.action:
                actual_fault_cause = "人为在站控室远程操纵ESD关阀命令"
            else:
                # 兜底使用原始场景描述
                actual_fault_cause = scenario.description

            conclusion = f"综合以上{len(inspection_steps)}项检查分析，确定故障原因为：{actual_fault_cause}。\n该故障属于{fault_type}系统故障，需要针对性处理。"

            # 构建完整的COT
            cot_parts = [
                f"### 故障诊断案例：安全阀异常关断",
                f"**故障现象**：现场安全阀突然关断，需要进行故障排查。",
                f"",
                f"## 推理过程：",
                "",
                *reasoning_steps,
                "",
                f"## 诊断结论：",
                conclusion,
                f"",
                f"**最终故障分类**：{fault_type}故障"
            ]

            return "\n".join(cot_parts)

        except Exception as e:
            return f"生成故障场景时出错：{str(e)}"


def main(output_to_console: bool = True, output_to_file: bool = True, num_cases: int = 10):
    """主程序

    Args:
        output_to_console: 是否输出到控制台
        output_to_file: 是否输出到文件
        num_cases: 生成案例数量
    """
    try:
        print("程序开始运行...")
        print(f"DataFrame形状: {df.shape}")

        # 创建故障诊断生成器
        print("创建故障诊断生成器...")
        generator = FaultDiagnosisGenerator(df)
        print("生成器创建成功")

        # 生成案例内容
        print("开始生成案例内容...")
        cases_content = []

        # 添加文件头部信息
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        header = f"""# 故障诊断COT案例集
生成时间：{current_time}
生成案例数量：{num_cases}个
系统：设备维检修故障诊断系统

{"="*80}

"""
        cases_content.append(header)
        print("文件头部信息添加完成")

        # 生成指定数量的故障推理过程
        for i in range(num_cases):
            print(f"正在生成案例 #{i+1}...")
            case_title = f"案例 #{i+1}"
            case_content = generator.generate_fault_scenario()
            case_separator = "\n" + "="*80 + "\n"

            # 组合单个案例内容
            full_case = f"{case_title}\n{case_content}{case_separator}"
            cases_content.append(full_case)
            print(f"案例 #{i+1} 生成完成")

            # 控制台输出（如果启用）
            if output_to_console:
                print(case_title)
                print(case_content)
                print(case_separator)

        # 保存到文件（如果启用）
        if output_to_file:
            # 确定输出文件路径 - 保存到当前项目文件夹
            output_filename = "故障诊断案例.txt"
            # 获取当前脚本所在目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            output_path = os.path.join(current_dir, output_filename)

            # 写入文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(''.join(cases_content))

            # 显示保存成功信息
            file_size = os.path.getsize(output_path)
            print(f"\n✅ 文件保存成功！")
            print(f"📁 文件路径：{output_path}")
            print(f"📊 文件大小：{file_size:,} 字节")
            print(f"📝 案例数量：{num_cases} 个")
            print(f"🕒 生成时间：{current_time}")

            # # 可选：创建备份文件名（包含时间戳）
            # backup_filename = f"故障诊断案例_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            # backup_path = os.path.join(current_dir, backup_filename)

            # with open(backup_path, 'w', encoding='utf-8') as f:
            #     f.write(''.join(cases_content))

            # print(f"💾 备份文件：{backup_path}")

    except Exception as e:
        print(f"❌ 程序运行出错：{str(e)}")
        import traceback
        print(f"详细错误信息：\n{traceback.format_exc()}")


if __name__ == "__main__":
    # 生成10个案例
    main(num_cases=10)